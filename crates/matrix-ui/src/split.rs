//! Split module for compatibility
//!
//! This module provides split functions for backward compatibility.

use floem::View;

/// Crea uno split orizzontale
pub fn hsplit<V1: View + 'static, V2: View + 'static>(
    left: V1,
    right: V2,
) -> impl View {
    use floem::views::h_stack;
    h_stack((left, right))
}

/// Crea uno split verticale  
pub fn vsplit<V1: View + 'static, V2: View + 'static>(
    top: V1,
    bottom: V2,
) -> impl View {
    use floem::views::v_stack;
    v_stack((top, bottom))
}
