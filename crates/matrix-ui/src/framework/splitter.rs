//! Splitter System for MATRIX_IDE
//!
//! This module provides professional splitters for resizable panels.

use std::sync::Arc;
use floem::{View, reactive::{RwSignal, SignalGet, SignalUpdate, create_rw_signal}, views::{container, empty, Decorators}, style::{Position, CursorStyle}};

/// Posizione del splitter
#[derive(Debug, Clone, PartialEq)]
pub enum SplitterPosition {
    Start(f64),
    End(f64),
    Center,
}

/// Sistema di gestione splitter
pub struct SplitterSystem {
    splitters: RwSignal<Vec<SplitterState>>,
}

/// Stato di un singolo splitter
#[derive(Debug, Clone)]
pub struct SplitterState {
    pub id: String,
    pub position: f64,
    pub is_dragging: bool,
    pub config: SplitterConfig,
}

impl SplitterSystem {
    pub fn new() -> Self {
        Self {
            splitters: create_rw_signal(Vec::new()),
        }
    }

    pub fn add_splitter(&self, id: String, config: SplitterConfig) -> RwSignal<f64> {
        let position = create_rw_signal(300.0); // Default position

        self.splitters.update(|splitters| {
            splitters.push(SplitterState {
                id,
                position: position.get(),
                is_dragging: false,
                config,
            });
        });

        position
    }
}

/// Splitter direction
#[derive(Debug, Clone, PartialEq)]
pub enum SplitterDirection {
    Horizontal,
    Vertical,
}

/// Splitter configuration
#[derive(Debug, Clone)]
pub struct SplitterConfig {
    pub direction: SplitterDirection,
    pub size: f64,
    pub min_size: f64,
    pub max_size: f64,
    pub snap_threshold: f64,
}

impl Default for SplitterConfig {
    fn default() -> Self {
        Self {
            direction: SplitterDirection::Vertical,
            size: 4.0,
            min_size: 100.0,
            max_size: 800.0,
            snap_threshold: 20.0,
        }
    }
}

/// Creates a professional splitter
pub fn create_splitter(
    config: SplitterConfig,
    position: RwSignal<f64>,
    is_dragging: RwSignal<bool>,
) -> impl View {
    let cursor = match config.direction {
        SplitterDirection::Horizontal => CursorStyle::RowResize,
        SplitterDirection::Vertical => CursorStyle::ColResize,
    };

    container(empty())
        .style(move |s| {
            s.position(Position::Absolute)
                .z_index(10)
                .cursor(cursor)
                .apply_if(config.direction == SplitterDirection::Vertical, |s| {
                    s.width(config.size).height_full()
                })
                .apply_if(config.direction == SplitterDirection::Horizontal, |s| {
                    s.height(config.size).width_full()
                })
                .hover(|s| s.background(floem::peniko::Color::rgb8(0, 122, 255)))
                .apply_if(is_dragging.get(), |s| s.background(floem::peniko::Color::rgb8(0, 122, 255)))
        })
}

/// Crea uno split orizzontale
pub fn hsplit<V1: View + 'static, V2: View + 'static>(
    left: V1,
    right: V2,
) -> impl View {
    use floem::views::h_stack;
    h_stack((left, right))
}

/// Crea uno split verticale
pub fn vsplit<V1: View + 'static, V2: View + 'static>(
    top: V1,
    bottom: V2,
) -> impl View {
    use floem::views::v_stack;
    v_stack((top, bottom))
}
