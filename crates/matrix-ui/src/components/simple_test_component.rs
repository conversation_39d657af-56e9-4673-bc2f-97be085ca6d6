//! Componente di test semplificato per verificare la compatibilità con Floem 0.2
//!
//! Questo componente implementa un semplice widget utilizzando solo le API essenziali
//! di Floem 0.2 per verificare la compatibilità base.

use std::sync::Arc;

use floem::{
    reactive::{create_rw_signal, RwSignal, SignalGet, SignalUpdate},
    views::{
        container, h_stack, label, button, scroll, v_stack, v_stack_from_iter, Decorators
    },
    style::{
        AlignItems, JustifyContent, Display, Position, CursorStyle
    },
    event::{Event, EventListener, EventPropagation},
    View,
    peniko::Color,
};

use crate::theme::{Theme, ThemeManager};
use crate::error::UiError;

/// Stato del componente di test
#[derive(Debug, Clone)]
pub struct TestComponentState {
    /// Contatore
    pub counter: i32,

    /// Messaggi
    pub messages: Vec<String>,

    /// Tema attuale
    pub theme_name: String,
}

impl Default for TestComponentState {
    fn default() -> Self {
        Self {
            counter: 0,
            messages: Vec::new(),
            theme_name: "default".to_string(),
        }
    }
}

/// Componente di test semplificato per verificare la compatibilità con Floem 0.2
pub struct SimpleTestComponent {
    /// Theme manager
    theme_manager: Arc<ThemeManager>,

    /// Stato del componente
    state: RwSignal<TestComponentState>,

    /// Callback quando il contatore cambia
    on_counter_change: Option<Box<dyn Fn(i32) + 'static>>,
}

impl SimpleTestComponent {
    /// Crea un nuovo componente di test
    pub fn new(theme_manager: Arc<ThemeManager>) -> Self {
        Self {
            theme_manager,
            state: create_rw_signal(TestComponentState::default()),
            on_counter_change: None,
        }
    }

    /// Imposta il callback per il cambiamento del contatore
    pub fn on_counter_change<F: Fn(i32) + 'static>(mut self, callback: F) -> Self {
        self.on_counter_change = Some(Box::new(callback));
        self
    }

    /// Incrementa il contatore
    pub fn increment(&self) {
        self.state.update(|state| {
            state.counter += 1;
            state.messages.push(format!("Contatore incrementato a {}", state.counter));

            // Notifica callback se presente
            if let Some(callback) = &self.on_counter_change {
                callback(state.counter);
            }
        });
    }

    /// Decrementa il contatore
    pub fn decrement(&self) {
        self.state.update(|state| {
            state.counter -= 1;
            state.messages.push(format!("Contatore decrementato a {}", state.counter));

            // Notifica callback se presente
            if let Some(callback) = &self.on_counter_change {
                callback(state.counter);
            }
        });
    }

    /// Crea la view del componente
    pub fn build(&self) -> Result<Box<dyn View>, UiError> {
        // Ottieni il tema attivo
        let theme = self.theme_manager.get_active_theme()?;

        // Segnali locali
        let state = self.state;

        // Cloni per le closure
        let component = self.clone();

        let view: Box<dyn View> = Box::new(
            container(
                v_stack((
                    // Titolo
                    label(|| "Componente di Test Floem 0.2".to_string())
                        .style(move |s| {
                            s.font_size(20.0)
                             .margin_bottom(16.0)
                             .color(theme.colors.text)
                        }),

                    // Contatore con pulsanti
                    h_stack((
                        // Pulsante decremento
                        button(label(|| "-".to_string()))
                            .on_click(move |_| {
                                component.decrement();
                                floem::event::EventPropagation::Continue
                            })
                            .style(move |s| {
                                s.padding(8.0)
                                 .border_radius(4.0)
                                 .background(theme.colors.button_bg)
                                 .color(theme.colors.button_fg)
                                 .cursor(CursorStyle::Pointer)
                            }),

                        // Valore contatore
                        label(move || format!("{}", state.get().counter))
                            .style(move |s| {
                                s.padding_horiz(16.0)
                                 .font_size(18.0)
                                 .color(theme.colors.text)
                                 .min_width(50.0)
                                 // text_align non più disponibile in Floem 0.2
                            }),

                        // Pulsante incremento
                        button(label(|| "+".to_string()))
                            .on_click(move |_| {
                                component.increment();
                                floem::event::EventPropagation::Continue
                            })
                            .style(move |s| {
                                s.padding(8.0)
                                 .border_radius(4.0)
                                 .background(theme.colors.button_bg)
                                 .color(theme.colors.button_fg)
                                 .cursor(CursorStyle::Pointer)
                            }),
                    ))
                    .style(move |s| {
                        s.align_items(Some(AlignItems::Center))
                         .margin_bottom(16.0)
                    }),

                    // Lista messaggi
                    label(|| "Log messaggi:".to_string())
                        .style(move |s| {
                            s.margin_bottom(8.0)
                             .color(theme.colors.text_secondary)
                        }),

                    scroll(
                        container(
                            v_stack((
                                label(move || format!("Messages: {}", state.get().messages.len()))
                                    .style(move |s| {
                                        s.padding(4.0)
                                         .color(theme.colors.text)
                                    })
                            ))
                            .style(move |s| {
                                s.width_full()
                            })
                        )
                        .style(move |s| {
                            s.width_full()
                             .height(200.0)
                             .border(1.0)
                             .border_color(theme.colors.border)
                             .border_radius(4.0)
                        })
                    )
                    .style(move |s| {
                        s.width_full()
                    }),

                    // Informazioni tema
                    label(move || format!("Tema attivo: {}",
                                         self.theme_manager.get_active_theme().map(|t| t.name).unwrap_or_else(|_| "sconosciuto".to_string())))
                        .style(move |s| {
                            s.margin_top(16.0)
                             .color(theme.colors.text_secondary)
                        }),
                ))
                .style(move |s| {
                    s.padding(16.0)
                     .width_full()
                     .height_full()
                     .background(theme.colors.background)
                })
            )
        );

        Ok(view)
    }
}

impl Clone for SimpleTestComponent {
    fn clone(&self) -> Self {
        Self {
            theme_manager: self.theme_manager.clone(),
            state: self.state,
            on_counter_change: None, // Non possiamo clonare la closure
        }
    }
}

// Funzione helper per creare la view
pub fn create_test_view(theme_manager: Arc<ThemeManager>) -> Result<Box<dyn View>, UiError> {
    let component = SimpleTestComponent::new(theme_manager);
    component.build()
}
