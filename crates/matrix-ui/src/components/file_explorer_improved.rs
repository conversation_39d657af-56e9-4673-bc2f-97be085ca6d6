//! File Explorer Component for MATRIX IDE
//!
//! Implementazione ottimizzata del File Explorer utilizzando Floem 0.2 API.
//! Supporta navigazione directory, apertura file, drag & drop e virtualizzazione.

use std::sync::Arc;
use std::path::{Path, PathBuf};
use std::collections::HashMap;

use floem::{
    reactive::{RwSignal, create_rw_signal, create_memo, SignalGet, SignalUpdate},
    views::{
        container, label, scroll, h_stack, v_stack, v_stack_from_iter,
        button, empty, Decorators, virtual_list, tab, tooltip
    },
    style::{CursorStyle, AlignItems, JustifyContent, Position, Display},
    event::{EventListener, Event, EventPropagation},
    View,
    peniko::Color,
};

// In Floem 0.2, BorderStyle è stato rimosso o rinominato
// Usiamo direttamente le stringhe per i bordi
use floem::style::Border;

use crate::theme::{Theme, ThemeManager};
use crate::error::UiError;

/// TreeNode - Elemento dell'albero file con informazioni sulla profondità
#[derive(Debug, Clone, PartialEq)]
struct TreeNode {
    /// Percorso completo
    path: PathBuf,

    /// Nome visualizzato
    name: String,

    /// È una directory?
    is_directory: bool,

    /// Livello di profondità nell'albero
    depth: usize,

    /// È selezionato?
    selected: bool,

    /// È espanso?
    expanded: bool,
}

/// Modello dell'albero file
struct TreeModel {
    /// Nodi visibili nell'albero
    nodes: Vec<TreeNode>,

    /// Indice del nodo radice
    root_index: Option<usize>,
}

impl TreeModel {
    /// Crea un nuovo modello ad albero vuoto
    fn new() -> Self {
        Self {
            nodes: Vec::new(),
            root_index: None,
        }
    }

    /// Aggiunge un nodo all'albero
    fn add_node(&mut self, node: TreeNode) -> usize {
        let index = self.nodes.len();
        self.nodes.push(node);
        index
    }

    /// Ottiene i nodi visibili in base alla posizione di scorrimento e altezza visibile
    fn visible_items(&self, scroll_pos: f64, visible_height: f64) -> Vec<TreeNode> {
        // Altezza approssimativa di ogni elemento
        const ITEM_HEIGHT: f64 = 24.0;

        // Calcola indici start/end approssimativi per la finestra di visualizzazione
        let start_idx = (scroll_pos / ITEM_HEIGHT).max(0.0) as usize;
        let visible_count = (visible_height / ITEM_HEIGHT).ceil() as usize + 1; // +1 per sicurezza
        let end_idx = (start_idx + visible_count).min(self.nodes.len());

        // Ritorna solo i nodi visibili
        self.nodes[start_idx..end_idx].to_vec()
    }

    /// Ritorna tutti i nodi
    fn all_nodes(&self) -> &[TreeNode] {
        &self.nodes
    }
}

/// Callback quando un file viene selezionato
pub type FileSelectedCallback = Box<dyn Fn(PathBuf) + Send + Sync>;

/// Operazioni sui file
#[derive(Debug, Clone)]
pub enum FileOperation {
    /// Apri file
    Open(PathBuf),

    /// Crea nuovo file
    Create(PathBuf),

    /// Rinomina file/directory
    Rename { source: PathBuf, new_name: String },

    /// Elimina file/directory
    Delete(PathBuf),

    /// Sposta file/directory
    Move { source: PathBuf, destination: PathBuf },
}

/// Callback per le operazioni sui file
pub type FileOperationCallback = Box<dyn Fn(FileOperation) + Send + Sync>;

/// Configurazione File Explorer
#[derive(Clone)]
pub struct FileExplorerConfig {
    /// Mostra file nascosti
    pub show_hidden: bool,

    /// Dimensione indentazione
    pub indent_size: f64,

    /// Altezza elemento
    pub item_height: f64,

    /// Usa icone personalizzate
    pub use_custom_icons: bool,
}

impl Default for FileExplorerConfig {
    fn default() -> Self {
        Self {
            show_hidden: false,
            indent_size: 16.0,
            item_height: 24.0,
            use_custom_icons: true,
        }
    }
}

/// File Explorer Component
pub struct FileExplorer {
    /// Theme manager
    theme_manager: Arc<ThemeManager>,

    /// Configurazione
    config: RwSignal<FileExplorerConfig>,

    /// Directory radice
    root_directory: RwSignal<Option<PathBuf>>,

    /// Lista elementi
    tree_model: RwSignal<TreeModel>,

    /// Posizione scorrimento
    scroll_position: RwSignal<f64>,

    /// Altezza visibile
    visible_height: RwSignal<f64>,

    /// Directory espanse
    expanded_dirs: RwSignal<HashMap<PathBuf, bool>>,

    /// Path selezionato
    selected_path: RwSignal<Option<PathBuf>>,

    /// Elemento trascinato
    dragged_item: RwSignal<Option<PathBuf>>,

    /// Target drag over
    drag_over_target: RwSignal<Option<PathBuf>>,

    /// Callback selezione file
    file_selected_callback: RwSignal<Option<FileSelectedCallback>>,

    /// Callback operazioni file
    file_operation_callback: RwSignal<Option<FileOperationCallback>>,

    /// Stato caricamento
    loading: RwSignal<bool>,

    /// Messaggio errore
    error: RwSignal<Option<String>>,
}

impl FileExplorer {
    /// Crea un nuovo file explorer
    pub fn new(theme_manager: Arc<ThemeManager>) -> Self {
        Self {
            theme_manager,
            config: create_rw_signal(FileExplorerConfig::default()),
            root_directory: create_rw_signal(None),
            tree_model: create_rw_signal(TreeModel::new()),
            scroll_position: create_rw_signal(0.0),
            visible_height: create_rw_signal(600.0),
            expanded_dirs: create_rw_signal(HashMap::new()),
            selected_path: create_rw_signal(None),
            dragged_item: create_rw_signal(None),
            drag_over_target: create_rw_signal(None),
            file_selected_callback: create_rw_signal(None),
            file_operation_callback: create_rw_signal(None),
            loading: create_rw_signal(false),
            error: create_rw_signal(None),
        }
    }

    /// Imposta il callback per la selezione file
    pub fn on_file_selected(&self, callback: impl Fn(PathBuf) + Send + Sync + 'static) {
        self.file_selected_callback.set(Some(Box::new(callback)));
    }

    /// Imposta il callback per le operazioni sui file
    pub fn on_file_operation(&self, callback: impl Fn(FileOperation) + Send + Sync + 'static) {
        self.file_operation_callback.set(Some(Box::new(callback)));
    }

    /// Imposta la configurazione
    pub fn with_config(self, config: FileExplorerConfig) -> Self {
        self.config.set(config);
        self
    }

    /// Imposta la directory root
    pub fn set_root_directory(&self, path: PathBuf) -> Result<(), UiError> {
        if !path.exists() || !path.is_dir() {
            return Err(UiError::FileError(format!(
                "Directory non valida: {}", path.display()
            )));
        }

        self.root_directory.set(Some(path.clone()));
        self.refresh()?;

        Ok(())
    }

    /// Aggiorna il file explorer
    pub fn refresh(&self) -> Result<(), UiError> {
        if let Some(root) = self.root_directory.get() {
            self.loading.set(true);
            self.error.set(None);

            // Costruisci il modello ad albero
            match self.build_tree_model(&root) {
                Ok(model) => {
                    self.tree_model.set(model);
                }
                Err(e) => {
                    self.error.set(Some(format!("Errore caricamento directory: {}", e)));
                }
            }

            self.loading.set(false);
        }

        Ok(())
    }

    /// Costruisce un modello ad albero efficiente invece di una lista piatta
    fn build_tree_model(&self, root_dir: &Path) -> Result<TreeModel, std::io::Error> {
        let mut model = TreeModel::new();
        let expanded_dirs = self.expanded_dirs.get();

        // Aggiungi la root se necessario
        let root_node = TreeNode {
            path: root_dir.to_path_buf(),
            name: root_dir.file_name()
                .map(|n| n.to_string_lossy().to_string())
                .unwrap_or_else(|| root_dir.to_string_lossy().to_string()),
            is_directory: true,
            depth: 0,
            selected: self.selected_path.get().as_ref() == Some(root_dir),
            expanded: true,  // Root sempre espansa
        };

        let root_idx = model.add_node(root_node);
        model.root_index = Some(root_idx);

        // Costruisci l'albero partendo dalla root in modo ricorsivo
        self.build_tree_node(&mut model, root_dir, 1, &expanded_dirs)?;

        Ok(model)
    }

    /// Costruisce un nodo dell'albero in modo ricorsivo
    fn build_tree_node(
        &self, 
        model: &mut TreeModel, 
        path: &Path, 
        depth: usize, 
        expanded_dirs: &HashMap<PathBuf, bool>
    ) -> Result<(), std::io::Error> {
        // Salta se la directory non è accessibile
        if !path.exists() || !path.is_dir() {
            return Ok(());
        }

        // Se la directory non è esplicitamente espansa, non carichiamo i suoi figli
        let is_expanded = path == self.root_directory.get().unwrap_or_default().as_path() || 
                         *expanded_dirs.get(path).unwrap_or(&false);

        if !is_expanded {
            return Ok(());
        }

        let config = self.config.get();

        if let Ok(entries) = std::fs::read_dir(path) {
            // Raccogli e ordina le entries
            let mut dirs = Vec::new();
            let mut files = Vec::new();

            for entry_result in entries {
                if let Ok(entry) = entry_result {
                    let entry_path = entry.path();
                    let name = entry_path.file_name()
                        .map(|n| n.to_string_lossy().to_string())
                        .unwrap_or_else(|| "Unknown".to_string());

                    // Salta file nascosti se necessario
                    if !config.show_hidden && name.starts_with('.') {
                        continue;
                    }

                    let is_dir = entry_path.is_dir();
                    let is_expanded = *expanded_dirs.get(&entry_path).unwrap_or(&false);
                    let selected = self.selected_path.get().as_ref() == Some(&entry_path);

                    let item = TreeNode {
                        path: entry_path.clone(),
                        name,
                        is_directory: is_dir,
                        depth,
                        selected,
                        expanded: is_expanded,
                    };

                    if is_dir {
                        dirs.push(item);
                    } else {
                        files.push(item);
                    }
                }
            }

            // Ordina per nome
            dirs.sort_by(|a, b| a.name.to_lowercase().cmp(&b.name.to_lowercase()));
            files.sort_by(|a, b| a.name.to_lowercase().cmp(&b.name.to_lowercase()));

            // Aggiungi prima le directory
            for dir in dirs {
                model.add_node(dir.clone());

                // Se la directory è espansa, aggiungi i suoi figli ricorsivamente
                if dir.expanded {
                    self.build_tree_node(model, &dir.path, depth + 1, expanded_dirs)?;
                }
            }

            // Poi aggiungi i file
            for file in files {
                model.add_node(file);
            }
        }

        Ok(())
    }

    /// Notifica selezione file
    fn notify_file_selected(&self, path: &Path) {
        if let Some(callback) = &self.file_selected_callback.get() {
            callback(path.to_path_buf());
        }
    }

    /// Notifica operazione file
    fn notify_file_operation(&self, operation: FileOperation) {
        if let Some(callback) = &self.file_operation_callback.get() {
            callback(operation);
        }
    }

    /// Toggle espansione di una directory
    fn toggle_expand_directory(&self, path: &Path) {
        let mut expanded_dirs = self.expanded_dirs.get();
        let is_expanded = *expanded_dirs.get(path).unwrap_or(&false);

        expanded_dirs.insert(path.to_path_buf(), !is_expanded);
        self.expanded_dirs.set(expanded_dirs);

        // Aggiorna la vista
        let _ = self.refresh();
    }

    /// Espande una directory
    fn expand_directory(&self, path: &Path, expanded: bool) {
        let mut expanded_dirs = self.expanded_dirs.get();
        expanded_dirs.insert(path.to_path_buf(), expanded);
        self.expanded_dirs.set(expanded_dirs);

        // Aggiorna la vista
        let _ = self.refresh();
    }

    /// Seleziona un file/directory
    fn select_path(&self, path: &Path) {
        self.selected_path.set(Some(path.to_path_buf()));
    }

    /// Implementa drag & drop per un nodo dell'albero
    fn implement_drag_drop(&self, view: impl View, node: TreeNode) -> impl View {
        let file_explorer = self.clone();
        let path = node.path.clone();
        let is_dir = node.is_directory;
        let dragged_item = self.dragged_item.clone();
        let drag_over_target = self.drag_over_target.clone();

        view
            .on_event(EventListener::DragStart, {
                let dragged_item = dragged_item.clone();
                let item_path = path.clone();

                move |event| {
                    if let Event::DragStart(_) = event {
                        dragged_item.set(Some(item_path.clone()));
                        // In una implementazione reale, imposteremmo il contenuto del drag
                        // event.set_drag_content("file-path", &item_path.to_string_lossy());
                    }
                    EventPropagation::Continue
                }
            })
            .on_event(EventListener::DragOver, {
                let drag_over_target = drag_over_target.clone();
                let item_path = path.clone();

                move |event| {
                    if let Event::DragOver(_) = event {
                        // Accetta solo drop su directory
                        if is_dir {
                            drag_over_target.set(Some(item_path.clone()));
                            // event.accept_drop(); // Richiede Floem 0.2 API
                        }
                    }
                    EventPropagation::Continue
                }
            })
            .on_event(EventListener::DragLeave, {
                let drag_over_target = drag_over_target.clone();

                move |_| {
                    drag_over_target.set(None);
                    EventPropagation::Continue
                }
            })
            .on_event(EventListener::Drop, {
                let target_path = path.clone();
                let dragged_item = dragged_item.clone();
                let drag_over_target = drag_over_target.clone();

                move |_| {
                    // Gestisci drop di file
                    if let Some(source_path) = dragged_item.get() {
                        if source_path != target_path && is_dir {  // Evita drop su se stesso
                            file_explorer.move_file_or_directory(&source_path, &target_path);
                        }
                    }

                    // Resetta stato drag & drop
                    dragged_item.set(None);
                    drag_over_target.set(None);

                    EventPropagation::Stop
                }
            })
    }

    /// Muove un file o directory in una directory target
    fn move_file_or_directory(&self, source: &Path, target_dir: &Path) {
        // Controlla che target sia directory
        if !target_dir.is_dir() {
            return;
        }

        // Crea percorso destinazione
        let file_name = source.file_name().unwrap_or_default();
        let destination = target_dir.join(file_name);

        // Evita sovrascritture
        if destination.exists() {
            // Qui potremmo mostrare una dialog di conferma
            return;
        }

        // Notifica operazione
        self.notify_file_operation(FileOperation::Move {
            source: source.to_path_buf(),
            destination: destination.to_path_buf(),
        });
    }

    /// Crea la vista per un singolo file/directory
    fn create_file_item_view(&self, node: TreeNode, theme: Theme) -> impl View {
        let indent = node.depth as f64 * self.config.get().indent_size;
        let icon = if node.is_directory {
            if node.expanded { "📂" } else { "📁" }
        } else {
            "📄"
        };

        let file_explorer = self.clone();
        let is_dir = node.is_directory;
        let path = node.path.clone();
        let is_drag_over = create_memo(move |_| {
            self.drag_over_target.get().as_ref() == Some(&path)
        });

        let item_view = container(
            h_stack((
                // Indentazione
                container(empty())
                    .style(move |s| s.width(indent)),

                // Icona
                label(move || icon.to_string())
                    .style(move |s| {
                        s.margin_right(4.0)
                         .font_size(14.0)
                    }),

                // Nome file/directory
                label(move || node.name.clone())
                    .style(move |s| {
                        s.font_size(13.0)
                         .color(if node.selected {
                             theme.colors.accent
                         } else {
                             theme.colors.text
                         })
                    }),
            ))
        )
        .style(move |s| {
            s.width_full()
             .height(self.config.get().item_height)
             .padding_vert(2.0)
             .padding_horiz(4.0)
             .cursor(CursorStyle::Pointer)
             .background(if node.selected {
                theme.colors.background_secondary
             } else if is_drag_over.get() {
                theme.colors.hover.multiply_alpha(0.7)
             } else {
                theme.colors.transparent
             })
             .hover(|s| s.background(theme.colors.hover))
             .apply_if(is_drag_over.get(), |s| 
                s.border(1.0)
                 .border_color(theme.colors.accent)
                 .border_style(BorderStyle::Dashed)
             )
        })
        .on_click_stop(move |_| {
            // Gestione click
            if is_dir {
                // Per directory: toggle espansione
                file_explorer.toggle_expand_directory(&path);
            } else {
                // Per file: seleziona e notifica
                file_explorer.select_path(&path);
                file_explorer.notify_file_selected(&path);
            }
        })
        .on_event(EventListener::PointerDoubleClick, move |_| {
            // Gestione doppio click
            if is_dir {
                // Per directory: espandi e seleziona
                file_explorer.expand_directory(&path, true);
                file_explorer.select_path(&path);
            } else {
                // Per file: apri
                file_explorer.notify_file_operation(FileOperation::Open(path.clone()));
            }
            EventPropagation::Stop
        });

        // Aggiungi supporto drag & drop
        self.implement_drag_drop(item_view, node)
    }

    /// Crea la vista principale del file explorer
    pub fn build(&self) -> impl View {
        let theme = self.theme_manager.get_active_theme().unwrap_or_default();
        let loading = self.loading;
        let error = self.error;
        let visible_height = self.visible_height.clone();
        let scroll_position = self.scroll_position.clone();

        container(
            v_stack((
                // Header
                container(
                    h_stack((
                        // Titolo
                        label(|| "Esplora File".to_string())
                            .style(move |s| {
                                s.font_size(13.0)
                                 .font_weight(floem::text::Weight::BOLD)
                                 .color(theme.colors.text)
                                 .flex_grow(1.0)
                            }),

                        // Pulsante refresh
                        button(label(|| "🔄"))
                            .on_click_stop({
                                let file_explorer = self.clone();
                                move |_| {
                                    let _ = file_explorer.refresh();
                                }
                            })
                            .style(move |s| {
                                s.padding(4.0)
                                 .font_size(12.0)
                                 .background(theme.colors.background_tertiary)
                                 .color(theme.colors.text)
                                 .border_radius(4.0)
                                 .hover(|s| s.background(theme.colors.hover))
                            }),
                    ))
                )
                .style(move |s| {
                    s.width_full()
                     .padding(8.0)
                     .background(theme.colors.background_secondary)
                     .border_bottom(1.0)
                     .border_color(theme.colors.border)
                }),

                // Contenuto principale
                container(
                    if loading.get() {
                        // Stato di caricamento
                        container(
                            label(|| "Caricamento...".to_string())
                                .style(move |s| {
                                    s.color(theme.colors.text_secondary)
                                     .font_size(13.0)
                                     .padding(16.0)
                                })
                        )
                    } else if let Some(err) = error.get() {
                        // Stato di errore
                        container(
                            label(move || format!("Errore: {}", err))
                                .style(move |s| {
                                    s.color(theme.colors.error)
                                     .font_size(13.0)
                                     .padding(16.0)
                                })
                        )
                    } else {
                        // Lista file virtualizzata
                        scroll(
                            container(
                                v_stack_from_iter({
                                    let model = self.tree_model.get();
                                    let theme_clone = theme.clone();

                                    // Usa solo gli elementi visibili per ottimizzare il rendering
                                    model.visible_items(scroll_position.get(), visible_height.get())
                                         .into_iter()
                                         .map(move |node| {
                                             self.create_file_item_view(node, theme_clone.clone())
                                         })
                                })
                            )
                            .style(|s| s.width_full())
                            .on_resize({
                                let visible_height = visible_height.clone();
                                move |rect| {
                                    visible_height.set(rect.height());
                                }
                            })
                        )
                        .style(|s| s.width_full().flex_grow(1.0))
                        .on_event(EventListener::Scroll, {
                            let scroll_position = scroll_position.clone();
                            move |event| {
                                if let Event::Scroll(e) = event {
                                    scroll_position.set(e.position.y);
                                }
                                EventPropagation::Continue
                            }
                        })
                    }
                )
                .style(|s| s.width_full().flex_grow(1.0))
            ))
        )
        .style(move |s| {
            s.width_full()
             .height_full()
             .background(theme.colors.background)
        })
    }
}
