//! AI Panel per MATRIX IDE
//!
//! Pannello per l'interazione con l'AI agent e la visualizzazione delle conversazioni.

use std::sync::Arc;
use std::collections::VecDeque;

use floem::{
    reactive::{RwSignal, create_rw_signal, SignalGet, SignalUpdate},
    views::{container, label, v_stack, scroll, Decorators},
    style::{AlignItems, JustifyContent},
    View,
};

use crate::{
    theme::{Theme, ThemeManager},
    error::UiError,
};

use matrix_core::Engine as CoreEngine;

/// Tipo di messaggio nella conversazione AI
#[derive(Debug, Clone, PartialEq)]
pub enum MessageType {
    User,
    Assistant,
    System,
    Error,
    Tool,
}

/// Stato del messaggio
#[derive(Debug, Clone, PartialEq)]
pub enum MessageStatus {
    Sent,
    Processing,
    Completed,
    Failed,
}

/// Messaggio nella conversazione AI
#[derive(Debug, Clone)]
pub struct AiMessage {
    pub id: String,
    pub message_type: MessageType,
    pub content: String,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub status: MessageStatus,
    pub metadata: Option<serde_json::Value>,
    pub tool_calls: Vec<ToolCall>,
    pub attachments: Vec<MessageAttachment>,
}

/// Chiamata a tool/funzione
#[derive(Debug, Clone)]
pub struct ToolCall {
    pub id: String,
    pub name: String,
    pub arguments: serde_json::Value,
    pub result: Option<serde_json::Value>,
    pub status: MessageStatus,
}

/// Allegato al messaggio
#[derive(Debug, Clone)]
pub struct MessageAttachment {
    pub attachment_type: AttachmentType,
    pub name: String,
    pub content: String,
    pub size: usize,
}

/// Tipo di allegato
#[derive(Debug, Clone, PartialEq)]
pub enum AttachmentType {
    Code,
    File,
    Image,
    Document,
    Data,
}

/// Pannello AI
pub struct AiPanel {
    core: Arc<CoreEngine>,
    theme_manager: Arc<ThemeManager>,
    messages: RwSignal<VecDeque<AiMessage>>,
    current_input: RwSignal<String>,
    is_processing: RwSignal<bool>,
    conversation_id: RwSignal<Option<String>>,
    auto_scroll: RwSignal<bool>,
    show_system_messages: RwSignal<bool>,
    max_messages: usize,
}

impl AiPanel {
    /// Crea un nuovo pannello AI
    pub fn new(core: Arc<CoreEngine>, theme_manager: Arc<ThemeManager>) -> Result<Self, UiError> {
        Ok(Self {
            core,
            theme_manager,
            messages: create_rw_signal(VecDeque::new()),
            current_input: create_rw_signal(String::new()),
            is_processing: create_rw_signal(false),
            conversation_id: create_rw_signal(None),
            auto_scroll: create_rw_signal(true),
            show_system_messages: create_rw_signal(false),
            max_messages: 100,
        })
    }

    /// Aggiunge un messaggio alla conversazione
    pub fn add_message(&self, message: AiMessage) {
        self.messages.update(|messages| {
            messages.push_back(message);
            
            // Mantieni solo gli ultimi max_messages messaggi
            while messages.len() > self.max_messages {
                messages.pop_front();
            }
        });
    }

    /// Invia un messaggio all'AI
    pub fn send_message(&self, content: String) -> Result<(), UiError> {
        if content.trim().is_empty() {
            return Err(UiError::GenericError("Il messaggio non può essere vuoto".to_string()));
        }

        let message = AiMessage {
            id: uuid::Uuid::new_v4().to_string(),
            message_type: MessageType::User,
            content: content.clone(),
            timestamp: chrono::Utc::now(),
            status: MessageStatus::Sent,
            metadata: None,
            tool_calls: Vec::new(),
            attachments: Vec::new(),
        };

        self.add_message(message);
        self.current_input.set(String::new());
        self.is_processing.set(true);

        // TODO: Implementare invio effettivo all'AI agent
        // Per ora simuliamo una risposta
        self.simulate_ai_response(content);

        Ok(())
    }

    /// Simula una risposta dell'AI (placeholder)
    fn simulate_ai_response(&self, user_message: String) {
        let response_message = AiMessage {
            id: uuid::Uuid::new_v4().to_string(),
            message_type: MessageType::Assistant,
            content: format!("Risposta simulata a: {}", user_message),
            timestamp: chrono::Utc::now(),
            status: MessageStatus::Completed,
            metadata: None,
            tool_calls: Vec::new(),
            attachments: Vec::new(),
        };

        self.add_message(response_message);
        self.is_processing.set(false);
    }

    /// Pulisce la conversazione
    pub fn clear_conversation(&self) {
        self.messages.set(VecDeque::new());
        self.conversation_id.set(None);
    }

    /// Imposta l'input corrente
    pub fn set_current_input(&self, input: String) {
        self.current_input.set(input);
    }

    /// Toggle visualizzazione messaggi di sistema
    pub fn toggle_system_messages(&self) {
        self.show_system_messages.update(|show| *show = !*show);
    }

    /// Toggle auto-scroll
    pub fn toggle_auto_scroll(&self) {
        self.auto_scroll.update(|auto| *auto = !*auto);
    }

    /// Crea la vista del pannello
    pub fn create_view(&self) -> impl View {
        let theme = self.theme_manager.get_active_theme().unwrap_or_default();
        let messages = self.messages;
        let current_input = self.current_input;
        let is_processing = self.is_processing;
        let show_system_messages = self.show_system_messages;

        container(
            v_stack((
                // Header
                self.create_header(),
                
                // Messages area
                scroll(
                    v_stack((
                        // Messages content
                        label(move || {
                            let filtered_messages = Self::filter_messages(
                                &messages.get(),
                                show_system_messages.get()
                            );
                            format!("Messaggi: {}", filtered_messages.len())
                        }),
                    ))
                    .style(move |s| s.flex_col().gap(8.0).padding(8.0))
                )
                .style(|s| s.flex_grow(1.0)),
                
                // Input area
                self.create_input_area(),
            ))
            .style(move |s| s.flex_col().size_full())
        )
        .style(move |s| {
            s.size_full()
                .background(theme.colors.background_secondary)
                .border_left(1.0)
                .border_color(theme.colors.border)
        })
    }

    /// Crea l'header del pannello
    fn create_header(&self) -> impl View {
        let theme = self.theme_manager.get_active_theme().unwrap_or_default();
        let is_processing = self.is_processing;
        
        container(
            label(move || {
                if is_processing.get() {
                    "AI Assistant (Elaborando...)".to_string()
                } else {
                    "AI Assistant".to_string()
                }
            })
            .style(move |s| {
                s.font_size(14.0)
                    .font_weight(600)
                    .color(theme.colors.text)
            })
        )
        .style(move |s| {
            s.padding(8.0)
                .background(theme.colors.background_tertiary)
                .border_bottom(1.0)
                .border_color(theme.colors.border)
                .justify_center()
        })
    }

    /// Crea l'area di input
    fn create_input_area(&self) -> impl View {
        let theme = self.theme_manager.get_active_theme().unwrap_or_default();
        let current_input = self.current_input;
        let is_processing = self.is_processing;
        
        container(
            v_stack((
                // Input field placeholder
                label(move || {
                    if is_processing.get() {
                        "Elaborando...".to_string()
                    } else {
                        format!("Input: {}", current_input.get())
                    }
                })
                .style(move |s| {
                    s.font_size(12.0)
                        .color(theme.colors.text_secondary)
                        .padding(8.0)
                }),
            ))
        )
        .style(move |s| {
            s.background(theme.colors.background_tertiary)
                .border_top(1.0)
                .border_color(theme.colors.border)
                .min_height(60.0)
        })
    }

    /// Filtra i messaggi in base alle impostazioni
    fn filter_messages(
        messages: &VecDeque<AiMessage>,
        show_system_messages: bool,
    ) -> Vec<AiMessage> {
        messages
            .iter()
            .filter(|msg| {
                show_system_messages || msg.message_type != MessageType::System
            })
            .cloned()
            .collect()
    }

    /// Ottiene il colore per il tipo di messaggio
    fn get_message_color(&self, message_type: &MessageType) -> floem::peniko::Color {
        match message_type {
            MessageType::User => floem::peniko::Color::rgb8(100, 149, 237),
            MessageType::Assistant => floem::peniko::Color::rgb8(40, 167, 69),
            MessageType::System => floem::peniko::Color::rgb8(108, 117, 125),
            MessageType::Error => floem::peniko::Color::rgb8(220, 53, 69),
            MessageType::Tool => floem::peniko::Color::rgb8(111, 66, 193),
        }
    }

    /// Esporta la conversazione
    pub fn export_conversation(&self) -> String {
        let messages = self.messages.get();
        messages
            .iter()
            .map(|msg| {
                format!(
                    "[{}] {}: {}",
                    msg.timestamp.format("%H:%M:%S"),
                    match msg.message_type {
                        MessageType::User => "User",
                        MessageType::Assistant => "Assistant",
                        MessageType::System => "System",
                        MessageType::Error => "Error",
                        MessageType::Tool => "Tool",
                    },
                    msg.content
                )
            })
            .collect::<Vec<_>>()
            .join("\n")
    }

    /// Ottiene le statistiche della conversazione
    pub fn get_conversation_stats(&self) -> ConversationStats {
        let messages = self.messages.get();
        
        let user_messages = messages.iter().filter(|m| m.message_type == MessageType::User).count();
        let assistant_messages = messages.iter().filter(|m| m.message_type == MessageType::Assistant).count();
        let system_messages = messages.iter().filter(|m| m.message_type == MessageType::System).count();
        let error_messages = messages.iter().filter(|m| m.message_type == MessageType::Error).count();
        
        ConversationStats {
            total_messages: messages.len(),
            user_messages,
            assistant_messages,
            system_messages,
            error_messages,
            is_processing: self.is_processing.get(),
        }
    }
}

/// Statistiche della conversazione
#[derive(Debug, Clone)]
pub struct ConversationStats {
    pub total_messages: usize,
    pub user_messages: usize,
    pub assistant_messages: usize,
    pub system_messages: usize,
    pub error_messages: usize,
    pub is_processing: bool,
}
