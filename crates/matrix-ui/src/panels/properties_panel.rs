//! Properties Panel per MATRIX IDE
//!
//! Pannello per visualizzare e modificare le proprietà degli elementi selezionati.

use std::sync::Arc;
use std::collections::HashMap;

use floem::{
    reactive::{RwSignal, create_rw_signal, SignalGet, SignalUpdate},
    views::{container, label, v_stack, scroll, Decorators},
    style::{AlignItems, JustifyContent},
    View,
};

use crate::{
    theme::{Theme, ThemeManager},
    error::UiError,
};

use matrix_core::Engine as CoreEngine;

/// Tipo di proprietà
#[derive(Debug, Clone, PartialEq)]
pub enum PropertyType {
    String,
    Number,
    Boolean,
    Color,
    Path,
    Array,
    Object,
}

/// Valore di una proprietà
#[derive(Debug, Clone)]
pub enum PropertyValue {
    String(String),
    Number(f64),
    Boolean(bool),
    Color(String),
    Path(String),
    Array(Vec<PropertyValue>),
    Object(HashMap<String, PropertyValue>),
}

/// Definizione di una proprietà
#[derive(Debug, Clone)]
pub struct Property {
    pub name: String,
    pub display_name: String,
    pub description: Option<String>,
    pub property_type: PropertyType,
    pub value: PropertyValue,
    pub editable: bool,
    pub category: Option<String>,
}

/// Pannello delle proprietà
pub struct PropertiesPanel {
    core: Arc<CoreEngine>,
    theme_manager: Arc<ThemeManager>,
    properties: RwSignal<Vec<Property>>,
    selected_item: RwSignal<Option<String>>,
    filter: RwSignal<String>,
    show_advanced: RwSignal<bool>,
}

impl PropertiesPanel {
    /// Crea un nuovo pannello delle proprietà
    pub fn new(core: Arc<CoreEngine>, theme_manager: Arc<ThemeManager>) -> Result<Self, UiError> {
        Ok(Self {
            core,
            theme_manager,
            properties: create_rw_signal(Vec::new()),
            selected_item: create_rw_signal(None),
            filter: create_rw_signal(String::new()),
            show_advanced: create_rw_signal(false),
        })
    }

    /// Aggiorna le proprietà per l'elemento selezionato
    pub fn update_properties(&self, item_id: String, properties: Vec<Property>) {
        self.selected_item.set(Some(item_id));
        self.properties.set(properties);
    }

    /// Pulisce le proprietà
    pub fn clear_properties(&self) {
        self.selected_item.set(None);
        self.properties.set(Vec::new());
    }

    /// Crea la vista del pannello
    pub fn create_view(&self) -> impl View {
        let theme = self.theme_manager.get_active_theme().unwrap_or_default();
        let properties = self.properties;
        let selected_item = self.selected_item;
        let filter = self.filter;

        container(
            v_stack((
                // Header
                self.create_header(),
                
                // Properties list
                scroll(
                    v_stack((
                        // Properties content will be added here
                        label(move || {
                            if selected_item.get().is_some() {
                                format!("Proprietà: {}", properties.get().len())
                            } else {
                                "Nessun elemento selezionato".to_string()
                            }
                        }),
                    ))
                    .style(move |s| s.flex_col().gap(4.0))
                )
                .style(|s| s.flex_grow(1.0)),
            ))
            .style(move |s| s.flex_col().size_full())
        )
        .style(move |s| {
            s.size_full()
                .background(theme.colors.background_secondary)
                .border_right(1.0)
                .border_color(theme.colors.border)
        })
    }

    /// Crea l'header del pannello
    fn create_header(&self) -> impl View {
        let theme = self.theme_manager.get_active_theme().unwrap_or_default();
        
        container(
            label(|| "Proprietà".to_string())
                .style(move |s| {
                    s.font_size(14.0)
                        .font_weight(600)
                        .color(theme.colors.text)
                })
        )
        .style(move |s| {
            s.padding(8.0)
                .background(theme.colors.background_tertiary)
                .border_bottom(1.0)
                .border_color(theme.colors.border)
                .justify_center()
        })
    }

    /// Aggiorna il filtro
    pub fn set_filter(&self, filter: String) {
        self.filter.set(filter);
    }

    /// Toggle visualizzazione proprietà avanzate
    pub fn toggle_advanced(&self) {
        self.show_advanced.update(|show| *show = !*show);
    }

    /// Ottiene le proprietà filtrate
    fn get_filtered_properties(&self) -> Vec<Property> {
        let properties = self.properties.get();
        let filter = self.filter.get();
        let show_advanced = self.show_advanced.get();

        properties
            .into_iter()
            .filter(|prop| {
                // Filtro per nome
                let name_match = filter.is_empty() || 
                    prop.name.to_lowercase().contains(&filter.to_lowercase()) ||
                    prop.display_name.to_lowercase().contains(&filter.to_lowercase());

                // Filtro per proprietà avanzate
                let advanced_match = show_advanced || prop.category.as_ref().map_or(true, |cat| cat != "advanced");

                name_match && advanced_match
            })
            .collect()
    }

    /// Modifica il valore di una proprietà
    pub fn update_property_value(&self, property_name: &str, new_value: PropertyValue) -> Result<(), UiError> {
        self.properties.update(|props| {
            if let Some(prop) = props.iter_mut().find(|p| p.name == property_name) {
                if prop.editable {
                    prop.value = new_value;
                }
            }
        });
        Ok(())
    }

    /// Ottiene il valore di una proprietà
    pub fn get_property_value(&self, property_name: &str) -> Option<PropertyValue> {
        self.properties.get()
            .iter()
            .find(|p| p.name == property_name)
            .map(|p| p.value.clone())
    }
}

impl Default for PropertyValue {
    fn default() -> Self {
        PropertyValue::String(String::new())
    }
}

impl std::fmt::Display for PropertyValue {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            PropertyValue::String(s) => write!(f, "{}", s),
            PropertyValue::Number(n) => write!(f, "{}", n),
            PropertyValue::Boolean(b) => write!(f, "{}", b),
            PropertyValue::Color(c) => write!(f, "{}", c),
            PropertyValue::Path(p) => write!(f, "{}", p),
            PropertyValue::Array(arr) => {
                write!(f, "[{}]", arr.iter().map(|v| v.to_string()).collect::<Vec<_>>().join(", "))
            }
            PropertyValue::Object(obj) => {
                write!(f, "{{{}}}", obj.iter().map(|(k, v)| format!("{}: {}", k, v)).collect::<Vec<_>>().join(", "))
            }
        }
    }
}
