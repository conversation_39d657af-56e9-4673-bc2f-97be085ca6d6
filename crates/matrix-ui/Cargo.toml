[package]
name = "matrix-ui"
version = "0.1.0"
edition = "2021"
authors = ["<PERSON><PERSON> <<EMAIL>>"]
license = "MIT"
repository = "https://github.com/matrix-ide/matrix-ui"
description = "UI Layer per MATRIX IDE, integra Lapce precompilato con Floem"

[[bin]]
name = "matrix-ide"
path = "src/bin/main.rs"

[dependencies]
# Dipendenze dal workspace
tokio.workspace = true
serde.workspace = true
serde_json.workspace = true
anyhow.workspace = true
thiserror.workspace = true
log.workspace = true
env_logger.workspace = true
async-trait.workspace = true
uuid.workspace = true
chrono.workspace = true

# Dipendenze UI dal workspace
floem.workspace = true
taffy.workspace = true
libloading = "0.8"

# Dipendenze interne dal workspace
matrix-core.workspace = true
matrix-graphics-api.workspace = true
matrix-plugin-loader.workspace = true

# Dipendenze specifiche per UI
image = "0.25"
notify.workspace = true
dirs.workspace = true
tokio-util = "0.7"
glob = "0.3"
regex = "1.10"
thread_local = "1.1"

[dev-dependencies]
# Testing framework
tokio-test = "0.4"
mockall = "0.12"
proptest = "1.4"
criterion = { version = "0.5", features = ["html_reports"] }
tempfile = "3.8"
serial_test = "3.0"
rstest = "0.18"
test-log = "0.2"

# Benchmarking
divan = "0.1"

[[bench]]
name = "ui_performance"
harness = false

[[bench]]
name = "theme_performance"
harness = false

[[bench]]
name = "component_performance"
harness = false
